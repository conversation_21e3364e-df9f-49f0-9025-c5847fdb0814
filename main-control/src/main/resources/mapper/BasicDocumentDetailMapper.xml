<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.basicData.BasicDocumentDetailMapper">
    <delete id="deleteByCode">
        delete from basic_document_detail where document_code = #{documentCode}
    </delete>
    <select id="queryMesDocumentDetail" resultType="com.ruoyi.vo.document.DocumentDetailResponse">
        SELECT b.*,c.material_name,c.material_sort,c.specifications,c.texture,a.supply_sales_code,a.transaction_code
        FROM basic_document_detail b
        left join basic_document_info a on a.id = b.document_code
        left join basic_material_info c on b.material_code = c.material_code
        WHERE 1 = 1
        <if test="keyWord != null and keyWord != ''">
            AND a.transaction_code = #{keyWord}
        </if>
        <if test="keySubWord != null and keySubWord.trim() != ''">
            and (
            b.material_code LIKE CONCAT('%', #{keySubWord}, '%') or c.material_name like CONCAT('%', #{keySubWord}, '%')
            )
        </if>
        <if test="keyFourWord != null and keyFourWord.trim() != ''">
            AND a.business_type = #{keyFourWord}
        </if>
        <if test="state != null">
            <choose>
                <when test="state == 0">
                    AND b.completed_num = 0
                </when>
                <when test="state == 1">
                    AND b.completed_num &gt; 0
                    AND b.completed_num &lt; b.quantity
                </when>
                <when test="state ==2">
                    AND b.completed_num = b.quantity
                </when>
                <otherwise>
                    AND 1 = 1
                </otherwise>
            </choose>
        </if>
        order by a.status asc,a.rece_time desc
    </select>
    <select id="selectByDocumentCode" resultType="com.ruoyi.domain.basicData.BasicDocumentDetail">
        SELECT *
        FROM basic_document_detail
        WHERE 1 = 1
        AND document_code = #{documentCode}
    </select>
</mapper>

