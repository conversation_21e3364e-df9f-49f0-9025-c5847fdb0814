package com.ruoyi.mapper.basicData;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.domain.basicData.BasicDocumentDetail;
import com.ruoyi.vo.document.DocumentDetailResponse;
import com.ruoyi.utils.QueryParamVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BasicDocumentDetailMapper extends BaseMapper<BasicDocumentDetail> {
    void deleteByCode(@Param("documentCode") String documentCode);

    List<DocumentDetailResponse> queryMesDocumentDetail(QueryParamVO queryParamVO);

    List<BasicDocumentDetail> selectByDocumentCode(@Param("documentCode") String documentCode);
}
