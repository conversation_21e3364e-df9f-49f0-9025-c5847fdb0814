package com.ruoyi.service.document;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.util.StringUtil;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.domain.basicData.BasicMaterialBatchInventory;
import com.ruoyi.domain.document.RecordAllotDetail;
import com.ruoyi.domain.document.RecordAllotInfo;
import com.ruoyi.mapper.document.RecordAllotDetailMapper;
import com.ruoyi.service.basicData.BasicMaterialBatchInventoryService;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.utils.ResponseResult;
import com.ruoyi.utils.constant.CommonConstant;
import com.ruoyi.vo.document.RecordAllotDetailVo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

@Service
public class AllocationDetailService extends ServiceImpl<RecordAllotDetailMapper, RecordAllotDetail> {
    @Resource
    RecordAllotDetailMapper recordAllotDetailMapper;
    @Resource
    AllocationInfoService allocationInfoService;
    @Resource
    BasicMaterialBatchInventoryService basicMaterialBatchInventoryService;

    /**
     * 根据bound_index删除记录
     *
     * @param boundIndex
     */
    public void removeByBoundIndex(String boundIndex) {
        LambdaQueryWrapper<RecordAllotDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RecordAllotDetail::getBoundIndex, boundIndex);
        this.remove(wrapper);
    }

    /**
     * 新增物料调拨详情
     *
     * @return
     */
    @Transactional
    public ResponseResult addRecordAllotInfoDetail(List<RecordAllotDetail> dtos) {
        for (RecordAllotDetail dto : dtos) {
            LambdaQueryWrapper<RecordAllotInfo> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(RecordAllotInfo::getBoundIndex, dto.getBoundIndex());
            RecordAllotInfo recordAllotInfo = allocationInfoService.getOne(wrapper);
            if (recordAllotInfo == null) {
                return ResponseResult.getErrorResult("该单据不存在，无法新增!");
            }
            // 检查状态，确保记录可以被修改
            if (!recordAllotInfo.getState().equals(CommonConstant.BoundStatus.PENDING_RE)) {
                return ResponseResult.getErrorResult("该单据正在审核中或已锁单，不能添加详情!");
            }
            RecordAllotDetail recordAllotDetail = new RecordAllotDetail();
            BeanUtils.copyProperties(dto, recordAllotDetail);
            recordAllotDetail.setBoundIndex(recordAllotInfo.getBoundIndex());
            // 获取物料库存信息，并冻结物料
            BasicMaterialBatchInventory materialBatchInventory = basicMaterialBatchInventoryService.getById(recordAllotDetail.getInventoryId());
            if (materialBatchInventory == null) {
                return ResponseResult.getErrorResult("调拨明细中的调出物料不存在！");
            }
            // 校验物料的可用数量是否足够
            if (materialBatchInventory.getAvailNum() < dto.getMaterialNum()) {
                return ResponseResult.getErrorResult("物料可用量为: " + materialBatchInventory.getAvailNum() + ", 数量不能大于可用数量!");
            }
            // 冻结物料数量
            basicMaterialBatchInventoryService.freezeMaterial(materialBatchInventory.getId(), dto.getMaterialNum());
            recordAllotInfo.setTotalNum(recordAllotInfo.getTotalNum() + dto.getMaterialNum());
            allocationInfoService.updateById(recordAllotInfo);
            this.save(recordAllotDetail);
        }
        // 返回成功结果
        return ResponseResult.getSuccessResult();
    }


    /**
     * 删除物料调拨详情
     *
     * @param recordAllotDetail
     * @return
     */
    @Transactional
    public ResponseResult deleteRecordAllotInfoDetail(RecordAllotDetail recordAllotDetail) {
        // 获取主键id，并检查是否为空
        String id = recordAllotDetail.getId();
        if (StringUtil.isEmpty(id)) {
            return ResponseResult.getErrorResult("主键id不能为空!");
        }
        RecordAllotDetail detail = this.getById(id);
        LambdaQueryWrapper<RecordAllotInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RecordAllotInfo::getBoundIndex, detail.getBoundIndex());
        RecordAllotInfo recordAllotInfo = allocationInfoService.getOne(wrapper);
        // 获取当前状态，判断是否允许删除
        Integer state = recordAllotInfo.getState();
        if (!state.equals(CommonConstant.BoundStatus.PENDING_RE)) {
            return ResponseResult.getErrorResult("该单据正在审核中或已锁单，不能删除详情!");
        }
        // 修改物料调拨单总数量
        Integer totalNum = recordAllotInfo.getTotalNum();
        recordAllotInfo.setTotalNum(totalNum - detail.getMaterialNum());
        allocationInfoService.updateById(recordAllotInfo);
        basicMaterialBatchInventoryService.unfreezeMaterial(detail.getInventoryId(), detail.getMaterialNum());
        this.removeById(id);
        // 返回成功结果
        return ResponseResult.getSuccessResult();
    }

    /**
     * 修改物料调拨详情
     *
     * @param dto
     * @return
     */
    @Transactional
    public ResponseResult updateRecordAllotInfoDetail(RecordAllotDetail dto) {
        // 判断主键id是否为空
        if (StringUtil.isEmpty(dto.getId())) {
            return ResponseResult.getErrorResult("主键id不能为空!");
        }
        RecordAllotDetail detail = this.getById(dto.getId());
        LambdaQueryWrapper<RecordAllotInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RecordAllotInfo::getBoundIndex, detail.getBoundIndex());
        RecordAllotInfo recordAllotInfo = allocationInfoService.getOne(wrapper);
        // 获取当前状态，检查是否允许修改
        Integer state = recordAllotInfo.getState();
        if (!state.equals(CommonConstant.BoundStatus.PENDING_RE)) {
            return ResponseResult.getErrorResult("该单据正在审核中或已锁单，不能修改!");
        }
        // 获取物料批次信息，解冻原有物料，冻结修改后的物料
        BasicMaterialBatchInventory materialBatchInventory = basicMaterialBatchInventoryService.getById(detail.getInventoryId());
        if (materialBatchInventory == null){
            return ResponseResult.getErrorResult("调拨原物料批次信息不存在");
        }
        // 校验物料的可用数量是否足够
        if (materialBatchInventory.getAvailNum() + detail.getMaterialNum() < dto.getMaterialNum()) {
            return ResponseResult.getErrorResult("物料可用量为: " + materialBatchInventory.getAvailNum() + ", 数量不能大于可用数量!");
        }
        // 解冻原有物料
        basicMaterialBatchInventoryService.unfreezeMaterial(materialBatchInventory.getId(), detail.getMaterialNum());
        // 冻结修改后的物料数量
        basicMaterialBatchInventoryService.freezeMaterial(materialBatchInventory.getId(), dto.getMaterialNum());
        this.updateByPrimaryKey(dto);
        // 修改对应调拨单的物料总量
        RecordAllotInfo recordAllotInfoToUpdate = allocationInfoService.getOne(
                new LambdaQueryWrapper<RecordAllotInfo>().eq(RecordAllotInfo::getBoundIndex, detail.getBoundIndex())
        );
        Integer totalNum = recordAllotInfoToUpdate.getTotalNum() - detail.getMaterialNum() + dto.getMaterialNum();
        recordAllotInfoToUpdate.setTotalNum(totalNum);
        allocationInfoService.updateById(recordAllotInfoToUpdate);
        // 返回成功结果
        return ResponseResult.getSuccessResult();
    }


    /**
     * 通过主键修改
     **/
    private void updateByPrimaryKey(RecordAllotDetail dto) {
        recordAllotDetailMapper.updateByPrimaryKey(dto);
    }

    /**
     * 查询物料调拨详情
     * @param queryParamVO
     * @return
     */
    public List<RecordAllotDetailVo> queryRecordAllotInfoDetail(QueryParamVO queryParamVO) {
        return recordAllotDetailMapper.queryRecordAllotInfoDetail(queryParamVO);
    }
}
