package com.ruoyi.service.document;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.util.StringUtil;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.domain.basicData.BasicMaterialBatchInventory;
import com.ruoyi.domain.basicData.RecordInoutDetail;
import com.ruoyi.domain.document.RecordUseOutbound;
import com.ruoyi.mapper.document.RecordUseOutboundMapper;
import com.ruoyi.service.basicData.BasicMaterialBatchInventoryService;
import com.ruoyi.service.work.RecordMaterialInoutService;
import com.ruoyi.utils.*;
import com.ruoyi.utils.constant.CommonConstant;
import com.ruoyi.vo.document.MaterialDetailDto;
import com.ruoyi.vo.document.RecordUseOutboundDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class WarehouseUseOutboundService extends ServiceImpl<RecordUseOutboundMapper, RecordUseOutbound> {

    @Resource
    RecordUseOutboundMapper recordUseOutboundMapper;
    @Resource
    BasicMaterialBatchInventoryService basicMaterialBatchInventoryService;
    @Resource
    RecordInoutDetailService recordInoutDetailService;
    @Resource
    RecordMaterialInoutService recordMaterialInoutService;

    public String getMaxIndex(String str) {
        AutoNum an = new AutoNum();
        String strDate = an.getStrDate();
        String mxstr = recordUseOutboundMapper.getMaxIndex(str + strDate + "%");
        if (StringUtils.isEmpty(mxstr)) {
            mxstr = str + DateAndTimeUtil.getNowTimeNoSeparator();
        }
        return an.getNum(str, mxstr);
    }

    /**
     * 添加仓库用料单信息
     */
    @Transactional
    public ResponseResult addRecordUseOutbound(RecordUseOutboundDto dto) {
        // 生成单据
        String boundIndex = getMaxIndex(CommonConstant.CodePrefix.WAREHOUSE_OUTBOUND_PREFIX);
        List<MaterialDetailDto> detailList = dto.getDetailList();
        if (detailList == null) {
            return ResponseResult.getErrorResult("仓库用料物料明细不能为空");
        }
        // 获取明细当中的所有物料数量
        int totalNum = 0;
        for (MaterialDetailDto detailDto : detailList) {
            totalNum += detailDto.getMaterialNum();
        }
        Date recordDate = DateAndTimeUtil.getNowDate();
        RecordUseOutbound inOut = new RecordUseOutbound();
        BeanUtils.copyProperties(dto, inOut);
        inOut.setRecorder(SecurityUtils.getUsername());
        inOut.setRecordDate(recordDate);
        inOut.setState(CommonConstant.BoundStatus.PENDING_RE);
        inOut.setTotalNum(totalNum);
        inOut.setBoundIndex(boundIndex);
        // 保存入库
        this.save(inOut);
        // 处理出入库记录详情
        List<RecordInoutDetail> details = new ArrayList<>();
        for (MaterialDetailDto materialDetailDto : detailList) {
            RecordInoutDetail recordInoutDetail = new RecordInoutDetail();
            BeanUtils.copyProperties(materialDetailDto, recordInoutDetail);
            recordInoutDetail.setBoundIndex(boundIndex);
            recordInoutDetail.setBoundType(CommonConstant.BoundType.CKYL);
            recordInoutDetail.setInventoryId(materialDetailDto.getInventoryId());
            boolean freezeResult = basicMaterialBatchInventoryService
                    .freezeMaterial(boundIndex,materialDetailDto.getInventoryId(),materialDetailDto.getMaterialNum());
            if(freezeResult){
                recordInoutDetailService.save(recordInoutDetail);
                details.add(recordInoutDetail);
            }
        }
        recordMaterialInoutService.insertByRecordInoutDetailList(details, CommonConstant.InoutType.FREEZE,
                CommonConstant.BusinessSource.WEB,recordDate);
        return ResponseResult.getSuccessResult();
    }


    /**
     * 修改仓库用料单信息
     */
    @Transactional
    public ResponseResult updateRecordUseOutbound(RecordUseOutbound dto) {
        if (StringUtil.isEmpty(dto.getId())) {
            return ResponseResult.getErrorResult("主键id不能为空!");
        }
        RecordUseOutbound recordUseOutbound = recordUseOutboundMapper.selectById(dto.getId());
        if (recordUseOutbound == null) {
            return ResponseResult.getErrorResult("该单据不存在，请刷新页面!");
        }
        if (dto.getState().equals(CommonConstant.BoundStatus.LOCKED)) {
            Date date = new Date();
            dto.setLockDate(date);
            //获取该单据下的详情物料信息
            List<RecordInoutDetail> list = recordInoutDetailService.list(new LambdaQueryWrapper<RecordInoutDetail>().eq(RecordInoutDetail::getBoundIndex, recordUseOutbound.getBoundIndex()));
            //出库修改物料库存数量
            for (RecordInoutDetail recordInoutDetail : list) {
                ResponseResult responseResult = basicMaterialBatchInventoryService.updateMaterialNum(recordInoutDetail);
                if (responseResult.getCode().equals(ResultMsg.errorCode)){
                    return responseResult;
                }
            }
            //增加物料出入库记录
            recordMaterialInoutService.insertByRecordInoutDetailList(list, CommonConstant.InoutType.OUT,
                    CommonConstant.BusinessSource.WEB,date);
        }
        recordUseOutboundMapper.updateByPrimaryKey(dto);
        return ResponseResult.getSuccessResult();
    }

    /**
     * 删除仓库用料信息
     */
    @Transactional
    public ResponseResult deleteRecordUseOutbound(RecordUseOutbound dto) {
        if (StringUtil.isEmpty(dto.getId())) {
            return ResponseResult.getErrorResult("主键id不能为空!");
        }
        RecordUseOutbound info = this.getById(dto.getId());
        if (info == null) {
            return ResponseResult.getErrorResult("该单据不存在!");
        }
        if (!info.getState().equals(CommonConstant.BoundStatus.PENDING_RE)) {
            return ResponseResult.getErrorResult("该单据正在审核中或已锁单,不能删除!");
        }
        //冻结的物料进行解冻
        List<RecordInoutDetail> list = recordInoutDetailService.list(new LambdaQueryWrapper<RecordInoutDetail>().eq(RecordInoutDetail::getBoundIndex, info.getBoundIndex()));
        basicMaterialBatchInventoryService.unfreezeMaterialBatch(list);
        //增加物料出入库解冻记录
        recordMaterialInoutService.insertByRecordInoutDetailList(list, CommonConstant.InoutType.RESTORE,
                CommonConstant.BusinessSource.WEB,DateAndTimeUtil.getNowDate());

        this.removeById(info.getId());
        //删除出入库记录详情信息
        recordInoutDetailService.removeByBoundIndex(info.getBoundIndex());
        return ResponseResult.getSuccessResult();
    }

    /**
     * 查询仓库用料单信息
     */
    public List<RecordUseOutbound> queryRecordUseOutbound(QueryParamVO queryParamVO) {
        LambdaQueryWrapper<RecordUseOutbound> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StringUtil.isNotEmpty(queryParamVO.getKeyWord()), RecordUseOutbound::getBoundIndex, queryParamVO.getKeyWord());
        if (queryParamVO.getState() != null) {
            wrapper.eq(RecordUseOutbound::getState, queryParamVO.getState());
        }
        wrapper.ge(StringUtil.isNotEmpty(queryParamVO.getBdate()), RecordUseOutbound::getRecordDate, queryParamVO.getBdate());
        wrapper.le(StringUtil.isNotEmpty(queryParamVO.getEdate()), RecordUseOutbound::getRecordDate, queryParamVO.getEdate());
        wrapper.orderByDesc(RecordUseOutbound::getRecordDate);
        return this.list(wrapper);
    }

    /**
     * 添加仓库用料物料明细
     */
    @Transactional
    public ResponseResult addRecordUseOutboundDetail(MaterialDetailDto dto) {
        RecordUseOutbound recordUseOutbound = this.queryObjByIndex(dto.getBoundIndex());
        if (recordUseOutbound == null) {
            return ResponseResult.getErrorResult("该单据不存在,无法新增!");
        }
        if (!recordUseOutbound.getState().equals(CommonConstant.BoundStatus.PENDING_RE)) {
            return ResponseResult.getErrorResult("该单据正在审核中或已锁单，不能添加详情!");
        }
        RecordInoutDetail recordInoutDetail = new RecordInoutDetail();
        BeanUtils.copyProperties(dto, recordInoutDetail);
        recordInoutDetail.setBoundType(CommonConstant.BoundType.CKYL);
        //新增详情后需要对数量进行相应的冻结
        boolean freezeResult = basicMaterialBatchInventoryService.freezeMaterial(dto.getInventoryId(), dto.getMaterialNum());
        if(freezeResult){
            recordUseOutbound.setTotalNum(recordUseOutbound.getTotalNum() + dto.getMaterialNum());
            recordUseOutboundMapper.updateById(recordUseOutbound);
            recordInoutDetailService.save(recordInoutDetail);

            recordMaterialInoutService.insertByRecordInoutDetail(recordInoutDetail, CommonConstant.InoutType.FREEZE,
                    CommonConstant.BusinessSource.WEB,DateAndTimeUtil.getNowDate());
            return ResponseResult.getSuccessResult();
        }else{
            return ResponseResult.getErrorResult("物料冻结失败，取消保存数据操作！");
        }
    }

    /**
     * 更新仓库用料物料明细
     *
     * @param dto
     * @return
     */
    @Transactional
    public ResponseResult updateRecordUseOutboundDetail(RecordInoutDetail dto) {
        if (StringUtil.isEmpty(dto.getId())) {
            return ResponseResult.getErrorResult("主键id不能为空!");
        }
        int needNum = dto.getMaterialNum();
        RecordInoutDetail detail = recordInoutDetailService.getById(dto.getId());
        BasicMaterialBatchInventory inventory = this.basicMaterialBatchInventoryService
                .getInventoryByContainerAndMaterial(detail.getContainerCode(),detail.getMaterialCode());
        int activeNum = inventory.getAvailNum() + detail.getMaterialNum();
        if(activeNum < needNum){
            return ResponseResult.getErrorResult("该物料的可用库存不足，请重新填入数量！目前可用库存量为：" + activeNum);
        }
        int resultNum = detail.getMaterialNum() - dto.getMaterialNum();
        int inoutType = CommonConstant.InoutType.RESTORE;
        if(resultNum > 0){
            //需减少冻结量
            basicMaterialBatchInventoryService.unfreezeMaterial(detail.getInventoryId(), resultNum);
        }else{
            resultNum = dto.getMaterialNum() - detail.getMaterialNum();
            inoutType = CommonConstant.InoutType.FREEZE;
            basicMaterialBatchInventoryService.freezeMaterial(detail.getInventoryId(), resultNum);
        }
        detail.setMaterialNum(resultNum);
        recordMaterialInoutService.insertByRecordInoutDetail(detail, inoutType,
                CommonConstant.BusinessSource.WEB,DateAndTimeUtil.getNowDate());

        recordInoutDetailService.updateByPrimaryKey(dto);
        //修改对应生产单物料总量
        RecordUseOutbound recordUseOutbound = this.queryObjByIndex(detail.getBoundIndex());
        if(recordUseOutbound != null){
            Integer totalNum = recordUseOutbound.getTotalNum() - detail.getMaterialNum() + dto.getMaterialNum();
            recordUseOutbound.setTotalNum(totalNum);
            this.updateById(recordUseOutbound);
        }
        return ResponseResult.getSuccessResult();
    }

    /**
     * 删除仓库用料物料明细
     */
    @Transactional
    public ResponseResult deleteRecordUseOutboundDetail(RecordInoutDetail recordInoutDetail) {
        String id = recordInoutDetail.getId();
        if (StringUtil.isEmpty(id)) {
            return ResponseResult.getErrorResult("主键id不能为空!");
        }
        RecordInoutDetail detail = recordInoutDetailService.getById(id);
        RecordUseOutbound recordUseOutbound = this.queryObjByIndex(detail.getBoundIndex());
        Integer state = recordUseOutbound.getState();
        if (!state.equals(CommonConstant.BoundStatus.PENDING_RE)) {
            return ResponseResult.getErrorResult("该单据正在审核中或已锁单，不能删除详情!");
        }
        //解冻物料
        boolean unFreezeResult = basicMaterialBatchInventoryService.unfreezeMaterial(detail.getInventoryId(), detail.getMaterialNum());
        if(unFreezeResult){
            //修改出入库总数量
            recordUseOutbound.setTotalNum(recordUseOutbound.getTotalNum() - detail.getMaterialNum());
            recordUseOutboundMapper.updateById(recordUseOutbound);
            recordInoutDetailService.removeById(id);

            recordMaterialInoutService.insertByRecordInoutDetail(detail, CommonConstant.InoutType.RESTORE,
                    CommonConstant.BusinessSource.WEB,DateAndTimeUtil.getNowDate());
        }else{
            return ResponseResult.getErrorResult("物料解冻失败，数据无法删除！请联系技术人员处理");
        }
        return ResponseResult.getSuccessResult();
    }

    public RecordUseOutbound queryObjByIndex(String boundIndex) {
        LambdaQueryWrapper<RecordUseOutbound> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RecordUseOutbound::getBoundIndex, boundIndex);
        return this.getOne(wrapper);
    }
}
