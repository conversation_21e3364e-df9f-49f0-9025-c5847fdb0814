package com.ruoyi.service.qc;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.LocalStringUtils;
import com.ruoyi.domain.basicData.BasicMaterialBatchInventory;
import com.ruoyi.domain.qc.QcIpqcDetailInfo;
import com.ruoyi.domain.qc.QcIpqcTaskInfo;
import com.ruoyi.mapper.qc.QcIpqcInfoMapper;
import com.ruoyi.utils.AutoNum;
import com.ruoyi.utils.DateAndTimeUtil;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.utils.ResponseResult;
import com.ruoyi.utils.constant.CommonConstant;
import com.ruoyi.vo.qc.QcIpqcInfoVo;
import com.ruoyi.vo.webRequest.BatchIdsReq;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * @Author: lhb
 * @CreateDate: 2025/6/24 11:45
 * @Description: 类描述
 */
@Service
public class QcIpqcInfoService extends ServiceImpl<QcIpqcInfoMapper, QcIpqcTaskInfo> {

    private static final Logger logger = LoggerFactory.getLogger(QcIpqcInfoService.class);

    @Resource
    private QcIpqcInfoMapper qcIpqcInfoMapper;

    @Resource
    private QcIpqcDetailInfoService qcIpqcDetailInfoService;


    /**
     * 新增
     */
    public String addQcIpqcInfo(QcIpqcTaskInfo qcIpqcTaskInfo){
        qcIpqcTaskInfo.setId(LocalStringUtils.getDataUUID());
        qcIpqcTaskInfo.setCreateTime(new Date());
        qcIpqcTaskInfo.setUpdateTime(new Date());
        String ipqcCode = getMaxIndex();
        qcIpqcTaskInfo.setIpqcCode(ipqcCode);
        this.qcIpqcInfoMapper.insert(qcIpqcTaskInfo);
        return ipqcCode;
    }

    /**
     * 删除质检任务（级联删除对应的详情）
     */
    @Transactional
    public ResponseResult deleteQcIpqcInfo(BatchIdsReq req) {
        try {
            List<String> ids = req.getIds();
            int successCount = 0;
            for (String id : ids) {
                QcIpqcTaskInfo taskInfo = this.getById(id);
                if (taskInfo == null) {
                    logger.warn("质检任务删除失败：未找到ID为 {} 的任务", id);
                    continue;
                }
                String ipqcCode = taskInfo.getIpqcCode();
                logger.info("开始删除质检任务：{}", ipqcCode);
                // 删除对应的质检详情
                LambdaQueryWrapper<QcIpqcDetailInfo> detailWrapper = new LambdaQueryWrapper<>();
                detailWrapper.eq(QcIpqcDetailInfo::getIpqcCode, ipqcCode);
                int detailCount = qcIpqcDetailInfoService.count(detailWrapper);
                if (detailCount > 0) {
                    boolean detailDeleted = qcIpqcDetailInfoService.remove(detailWrapper);
                    if (!detailDeleted) {
                        logger.warn("质检任务删除失败：删除质检详情失败，任务编码为 {}", ipqcCode);
                        continue;
                    }
                }
                boolean taskDeleted = this.removeById(id);
                if (taskDeleted) {
                    successCount++;
                }
            }
            if (successCount >= ids.size()) {
                return ResponseResult.getSuccessResult("删除成功", null);
            } else if (successCount > 0) {
                return ResponseResult.getErrorResult(String.format("部分删除成功，成功删除 %d/%d 条记录", successCount, ids.size()));
            } else {
                return ResponseResult.getErrorResult("删除失败，请重试");
            }
        } catch (Exception e) {
            logger.error("删除质检任务时发生异常", e.getMessage());
            return ResponseResult.getErrorResult("删除失败：" + e.getMessage());
        }
    }

    /**
     * 更新质检详情
     */
    public ResponseResult uptQcIpqcInfo(QcIpqcTaskInfo qcIpqcTaskInfo) {
        qcIpqcTaskInfo.setUpdateTime(new Date());
        int count = this.qcIpqcInfoMapper.updateById(qcIpqcTaskInfo);
        if(count >= 1){
            return ResponseResult.getSuccessResult();
        }
        return ResponseResult.getErrorResult("数据更新失败，请重试");
    }

    /**
     * 分页
     */
    public List<QcIpqcInfoVo> queryQcIpqcInfo(QueryParamVO param) {
        return this.qcIpqcInfoMapper.queryQcIpqcInfo(param);
    }
    

    /**
     * 获取最大的质检模板
     */
    public String getMaxIndex() {
        String headstr = CommonConstant.TaskPrefix.QC_IOC_NO_HEAD;
        AutoNum an = new AutoNum();
        // 获得最大index编码
        String strDate = an.getStrDate();
        String mxstr = this.qcIpqcInfoMapper.getMaxIndex(headstr + strDate + "%");
        if (org.apache.commons.lang3.StringUtils.isEmpty(mxstr)) {
            mxstr = headstr + DateAndTimeUtil.getNowTimeNoSeparator();
        }
        return an.getNum(headstr, mxstr);
    }


    /**
     * 根据质检编码集合查询所有质检数据
     */
    public List<QcIpqcTaskInfo> queryQcIpqcInfoByCodes(List<String> param) {
        if (CollectionUtils.isEmpty(param)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<QcIpqcTaskInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(QcIpqcTaskInfo::getIpqcCode, param);
        return qcIpqcInfoMapper.selectList(wrapper);
    }

    /**
     * 根据质检编码查询最新的质检数据（按创建时间倒序取第一条）
     */
    public QcIpqcTaskInfo queryQcIpqcInfoByIpqcCode(String ipqcCode) {
        LambdaQueryWrapper<QcIpqcTaskInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(QcIpqcTaskInfo::getIpqcCode, ipqcCode)
                .orderByDesc(QcIpqcTaskInfo::getCreateTime)
                .last("LIMIT 1");
        return qcIpqcInfoMapper.selectOne(wrapper);
    }


    public List<QcIpqcTaskInfo> queryQcIpqcInfoByTemplateCode(String templateCode) {
        LambdaQueryWrapper<QcIpqcTaskInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(QcIpqcTaskInfo::getTemplateCode, templateCode);
        return qcIpqcInfoMapper.selectList(wrapper);
    }
}
