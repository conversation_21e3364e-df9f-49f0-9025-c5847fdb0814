package com.ruoyi.controller.basicData;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.domain.basicData.RecordInoutDetail;
import com.ruoyi.domain.document.*;
import com.ruoyi.service.basicData.BasicMaterialBatchInventoryService;
import com.ruoyi.service.document.*;
import com.ruoyi.service.sys.ProjectSysConfigService;
import com.ruoyi.service.work.ExcelExportService;
import com.ruoyi.service.work.RecordMaterialInoutService;
import com.ruoyi.service.work.TaskReportBBSource;
import com.ruoyi.utils.GsonUtils;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.utils.ReportUtil;
import com.ruoyi.utils.ResponseResult;
import com.ruoyi.utils.constant.CommonConstant;
import com.ruoyi.vo.document.*;
import com.ruoyi.vo.report.RecordMaterialInoutVo;
import com.ruoyi.vo.warehouse.*;
import com.ruoyi.vo.webResponse.dto.MaterialDetailExcelData;
import net.sf.jasperreports.engine.JRException;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.JasperReport;
import net.sf.jasperreports.engine.util.JRLoader;
import org.springframework.beans.BeanUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.util.*;

/**
 * 对接前端功能操作
 */
@RestController
@RequestMapping("/speedbot/wms/web")
public class WebWorkController extends BaseController {

    @Resource
    private InventoryCheckService inventoryCheckService;
    @Resource
    private WarehouseUseOutboundService warehouseUseOutboundService;
    @Resource
    private AllocationInfoService allocationInfoService;
    @Resource
    private AllocationDetailService allocationDetailService;
    @Resource
    RecordMaterialInoutService recordMaterialInoutService;
    @Resource
    private BasicMaterialBatchInventoryService basicMaterialBatchInventoryService;
    @Resource
    ProjectSysConfigService ProjectSysConfigService;
    @Resource
    private RecordInoutDetailService recordInoutDetailService;
    @Resource
    private ExcelExportService excelExportService;


    /**
     * 新增库存盘点功能
     */
    @PostMapping("/addInventoryRecord")
    @Log(title = "库存盘点", businessType = BusinessType.INSERT)
    public ResponseResult addInventoryRecord(@RequestBody RecordInventoryDto recordInventoryDto) {
        logger.info("新增库存盘点信息:" + GsonUtils.toJsonString(recordInventoryDto));
        return inventoryCheckService.addInventoryRecord(recordInventoryDto);
    }

    /**
     * 查询库存盘点信息
     */
    @PostMapping("/queryInventoryRecord")
    public TableDataInfo queryInventoryRecord(@RequestBody QueryParamVO queryParamVO) {
        PageHelper.startPage(queryParamVO.getPageNum(), queryParamVO.getPageSize(), "");
        List<RecordInventoryInfoVo> list = inventoryCheckService.queryInventoryRecord(queryParamVO);
        return this.getDataTable(list);
    }


    /**
     * 查询库存盘点详情信息
     */
    @PostMapping("/queryInventoryRecordDetail")
    public TableDataInfo queryInventoryRecordDetail(@RequestBody QueryParamVO queryParamVO) {
        PageHelper.startPage(queryParamVO.getPageNum(), queryParamVO.getPageSize(), "");
        List<RecordInventoryDetailVo> list = inventoryCheckService.queryInventoryRecordDetail(queryParamVO);
        return this.getDataTable(list);
    }

    /**
     * 更新库存盘点详情信息
     */
    @PostMapping("/updateRecordInventoryDetail")
    @Log(title = "库存盘点信息", businessType = BusinessType.UPDATE)
    public ResponseResult updateRecordInventoryDetail(@RequestBody List<RecordInventoryDetail> param) {
        return inventoryCheckService.updateRecordInventoryDetail(param);
    }

    /**
     * 新增库存盘点详情信息
     */
    @PostMapping("/addRecordInventoryDetail")
    @Log(title = "库存盘点信息", businessType = BusinessType.UPDATE)
    public ResponseResult updateRecordInventoryDetail(@RequestBody RecordInventoryDetail param) {
        return inventoryCheckService.addRecordInventoryDetail(param);
    }

    /**
     * 更新库存盘点信息
     */
    @PostMapping("/updateRecordInventoryInfo")
    @Log(title = "库存盘点信息", businessType = BusinessType.UPDATE)
    public ResponseResult updateRecordInventoryInfo(@RequestBody RecordInventoryInfo recordInventoryInfo) {
        return inventoryCheckService.updateRecordInventoryInfo(recordInventoryInfo);
    }

    /**
     * 删除库存盘点信息
     */
    @PostMapping("/deleteRecordInventoryInfo")
    @Log(title = "库存盘点信息", businessType = BusinessType.DELETE)
    public ResponseResult deleteRecordInventoryInfo(@RequestBody RecordInventoryInfo recordInventoryInfo) {
        return inventoryCheckService.deleteRecordInventoryInfo(recordInventoryInfo);
    }

    /**
     * 删除库存盘点信息详情
     */
    @PostMapping("/deleteRecordInventoryDetail")
    @Log(title = "库存盘点信息", businessType = BusinessType.DELETE)
    public ResponseResult deleteRecordInventoryDetail(@RequestBody RecordInventoryDetail recordInventoryDetail) {
        return inventoryCheckService.deleteRecordInventoryDetail(recordInventoryDetail);
    }

    /**
     * 新增仓库用料信息
     */
    @PostMapping("/addRecordUseOutbound")
    @Log(title = "仓库用料", businessType = BusinessType.INSERT)
    public ResponseResult addRecordUseOutbound(@RequestBody RecordUseOutboundDto dto) {
        return warehouseUseOutboundService.addRecordUseOutbound(dto);
    }

    /**
     * 更新仓库用料信息
     */
    @PostMapping("/updateRecordUseOutbound")
    @Log(title = "仓库用料", businessType = BusinessType.UPDATE)
    public ResponseResult updateRecordUseOutbound(@RequestBody RecordUseOutbound recordUseOutbound) {
        return warehouseUseOutboundService.updateRecordUseOutbound(recordUseOutbound);
    }

    /**
     * 删除仓库用料信息
     */
    @PostMapping("/deleteRecordUseOutbound")
    @Log(title = "仓库用料", businessType = BusinessType.DELETE)
    public ResponseResult deleteRecordUseOutbound(@RequestBody RecordUseOutbound recordUseOutbound) {
        return warehouseUseOutboundService.deleteRecordUseOutbound(recordUseOutbound);
    }

    /**
     * 查询仓库用料信息
     */
    @PostMapping("/queryRecordUseOutbound")
    public TableDataInfo queryRecordUseOutbound(@RequestBody QueryParamVO queryParamVO) {
        PageHelper.startPage(queryParamVO.getPageNum(), queryParamVO.getPageSize(), "");
        List<RecordUseOutbound> list = warehouseUseOutboundService.queryRecordUseOutbound(queryParamVO);
        return this.getDataTable(list);
    }

    /**
     * 新增仓库用料物料详情
     */
    @PostMapping("/addRecordUseOutboundDetail")
    @Log(title = "仓库用料", businessType = BusinessType.INSERT)
    public ResponseResult addRecordUseOutboundDetail(@RequestBody MaterialDetailDto dto) {
        return warehouseUseOutboundService.addRecordUseOutboundDetail(dto);
    }

    /**
     * 更新仓库用料物料详情
     */
    @PostMapping("/updateRecordUseOutboundDetail")
    @Log(title = "仓库用料", businessType = BusinessType.UPDATE)
    public ResponseResult updateRecordUseOutboundDetail(@RequestBody RecordInoutDetail dto) {
        return warehouseUseOutboundService.updateRecordUseOutboundDetail(dto);
    }

    /**
     * 删除仓库用料物料详情
     */
    @PostMapping("/deleteRecordUseOutboundDetail")
    @Log(title = "仓库用料", businessType = BusinessType.DELETE)
    public ResponseResult deleteRecordUseOutboundDetail(@RequestBody RecordInoutDetail recordInoutDetail) {
        return warehouseUseOutboundService.deleteRecordUseOutboundDetail(recordInoutDetail);
    }

    /**
     * 新增物料调拨信息
     */
    @PostMapping("/addRecordAllotInfo")
    @Log(title = "物料调拨", businessType = BusinessType.INSERT)
    public ResponseResult addRecordAllotInfo(@RequestBody RecordAllotInfoDto dto) {
        return allocationInfoService.addRecordAllotInfo(dto);
    }

    /**
     * 更新物料调拨信息
     */
    @PostMapping("/updateRecordAllotInfo")
    @Log(title = "物料调拨", businessType = BusinessType.UPDATE)
    public ResponseResult updateRecordAllotInfo(@RequestBody RecordAllotInfo recordAllotInfo) {
        return allocationInfoService.updateRecordAllotInfo(recordAllotInfo);
    }

    /**
     * 删除物料调拨信息
     */
    @PostMapping("/deleteRecordAllotInfo")
    @Log(title = "物料调拨", businessType = BusinessType.DELETE)
    public ResponseResult deleteRecordAllotInfo(@RequestBody RecordAllotInfo recordAllotInfo) {
        return allocationInfoService.deleteRecordAllotInfo(recordAllotInfo);
    }

    /**
     * 查询物料调拨信息
     */
    @PostMapping("/queryRecordAllotInfo")
    public TableDataInfo queryRecordAllotInfo(@RequestBody QueryParamVO queryParamVO) {
        PageHelper.startPage(queryParamVO.getPageNum(), queryParamVO.getPageSize(), "");
        List<RecordAllotInfo> list = allocationInfoService.queryRecordAllotInfo(queryParamVO);
        return this.getDataTable(list);
    }

    /**
     * 新增物料调拨详情
     */
    @PostMapping("/addRecordAllotDetail")
    @Log(title = "物料调拨", businessType = BusinessType.INSERT)
    public ResponseResult addRecordAllotInfoDetail(@RequestBody List<RecordAllotDetail> dto) {
        return allocationDetailService.addRecordAllotInfoDetail(dto);
    }

    /**
     * 更新物料调拨详情
     */
    @PostMapping("/updateRecordAllotDetail")
    @Log(title = "物料调拨", businessType = BusinessType.UPDATE)
    public ResponseResult updateRecordAllotInfoDetail(@RequestBody RecordAllotDetail dto) {
        return allocationDetailService.updateRecordAllotInfoDetail(dto);
    }

    /**
     * 删除物料调拨详情
     */
    @PostMapping("/deleteRecordAllotDetail")
    @Log(title = "物料调拨", businessType = BusinessType.DELETE)
    public ResponseResult deleteRecordAllotInfoDetail(@RequestBody RecordAllotDetail recordAllotDetail) {
        return allocationDetailService.deleteRecordAllotInfoDetail(recordAllotDetail);
    }

    /**
     * 查询物料调拨详情
     */
    @PostMapping("/queryRecordAllotDetail")
    public TableDataInfo queryRecordAllotInfoDetail(@RequestBody QueryParamVO queryParamVO) {
        PageHelper.startPage(queryParamVO.getPageNum(), queryParamVO.getPageSize(), "");
        List<RecordAllotDetailVo> list = allocationDetailService.queryRecordAllotInfoDetail(queryParamVO);
        return this.getDataTable(list);
    }

    /**
     * 出入库记录导出
     */
    @GetMapping("/recordMaterialInoutExport")
    public void recordMaterialInoutExport(HttpServletResponse response,
                                                @RequestParam(required = false) String keyWord,
                                                @RequestParam(required = false) String keySubWord,
                                                @RequestParam(required = false) String keyThirdWord,
                                                @RequestParam(required = false) Integer state,
                                                @RequestParam(required = false) Integer stateSub,
                                                @RequestParam(required = false) String bdate,
                                                @RequestParam(required = false) String edate) throws IOException{
        QueryParamVO queryParamVO = new QueryParamVO();
        queryParamVO.setKeyWord(keyWord);
        queryParamVO.setKeySubWord(keySubWord);
        queryParamVO.setKeyThirdWord(keyThirdWord);
        queryParamVO.setState(state);
        queryParamVO.setStateSub(stateSub);
        queryParamVO.setBdate(bdate);
        queryParamVO.setEdate(edate);
        List<RecordMaterialInoutVo> list = recordMaterialInoutService.queryRecordMaterialInout(queryParamVO);
        excelExportService.exportExcel(response, list, "出入库记录" + DateUtils.getDate(), RecordMaterialInoutVo.class);
    }

    /**
     * 库存预警信息导出
     */
    @GetMapping("/materialAlertInfoExport")
    public void materialAlertInfoExport(HttpServletResponse response,
                                              @RequestParam(required = false) String keyWord,
                                              @RequestParam(required = false) Integer state,
                                              @RequestParam(required = false) Integer stateSub
                                              ) throws IOException {
        QueryParamVO queryParamVO = new QueryParamVO();
        queryParamVO.setKeyWord(keyWord);
        queryParamVO.setState(state);
        queryParamVO.setStateSub(stateSub);
        List<BasicMaterialAlertInfo> list = basicMaterialBatchInventoryService.queryMaterialAlertInfo(queryParamVO);
        excelExportService.exportExcel(response, list, "库存预警信息" + DateUtils.getDate(), BasicMaterialAlertInfo.class);
    }

    /**
     * 物料库存数量信息导出
     */
    @Log(title = "物料数量信息", businessType = BusinessType.EXPORT)
    @GetMapping("/materialNumDateExport")
    public void  materialNumDateExport(HttpServletResponse response,
                                       @RequestParam(required = false) String keyWord,
                                       @RequestParam(required = false) String keySubWord) throws IOException {
        QueryParamVO queryParamVO = new QueryParamVO();
        queryParamVO.setKeyWord(keyWord);
        queryParamVO.setKeySubWord(keySubWord);
        List<BasicMaterialNumInfo> list = basicMaterialBatchInventoryService.queryMaterialNumDate(queryParamVO);
        excelExportService.exportExcel(response, list, "物料库存信息" + DateUtils.getDate(), BasicMaterialNumInfo.class);
    }

    /**
     * 物料过期预警信息导出
     */
    @Log(title = "物料过期预警", businessType = BusinessType.EXPORT)
    @GetMapping("/materialExpirationAlertExport")
    public void materialExpirationAlertExport(HttpServletResponse response,
                                                    @RequestParam(required = false) String keyWord,
                                                    @RequestParam(required = false) String keySubWord,
                                                    @RequestParam(required = false) Integer state
                                                    ) throws IOException {
        QueryParamVO queryParamVO = new QueryParamVO();
        queryParamVO.setKeyWord(keyWord);
        queryParamVO.setKeySubWord(keySubWord);
        queryParamVO.setState(state);
        List<MaterialAlertInfo> list = basicMaterialBatchInventoryService.queryMaterialExpirationAlert(queryParamVO);
        excelExportService.exportExcel(response,list, "物料过期预警" + DateUtils.getDate(), MaterialAlertInfo.class);
    }


    /**
     * 物料调拨单导出（pdf）
     */
    @PostMapping("/exportAllotInfoPdf")
    public void exportAllotInfoPdf(HttpServletResponse response, @RequestBody JSONObject idInJson) throws UnsupportedEncodingException {
        String bound_index = idInJson.getString("bound_index");
        QueryParamVO query = new QueryParamVO();
        query.setKeyWord(bound_index);
        List<RecordAllotInfo> recordAllotInfos = allocationInfoService.queryRecordAllotInfo(query);
        RecordAllotInfo inout = recordAllotInfos.get(0);
        Integer state = inout.getState();
        Integer rule = inout.getTransferRule();
        Map<String, Object> parameters = new HashMap<String, Object>();
        parameters.put("BOUND_INDEX", inout.getBoundIndex());
        parameters.put("TOTAL_NUM", inout.getTotalNum());
        parameters.put("STATE", state == 0 ? "未送审" : state == 1 ? "审核中" : "锁单");
        parameters.put("REMARK", inout.getRemark());
        parameters.put("RECORDER", inout.getRecorder());
        parameters.put("REASON", inout.getReasons());
        parameters.put("RULES", rule == 0 ? "同物料调拨" : "不同物料调拨");
        parameters.put("LOCK_DATE", Optional.ofNullable(inout.getLockDate()).map(date -> DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", date)).orElse(""));
        parameters.put("LOGO",Optional.ofNullable(ProjectSysConfigService.getSysConfigValByType(CommonConstant.ProjectSysConfig.LOGO_PATH)).orElse("/home/<USER>/control_system/speedbot.png"));

        List<MaterialDetailExcelData> pdfList = new ArrayList<>();
        QueryParamVO queryParamVO = new QueryParamVO();
        queryParamVO.setKeyWord(bound_index);
        List<RecordAllotDetailVo> list = allocationDetailService.queryRecordAllotInfoDetail(queryParamVO);
        for (RecordAllotDetailVo recordAllotDetailVo : list) {
            MaterialDetailExcelData param = new MaterialDetailExcelData();
            BeanUtils.copyProperties(recordAllotDetailVo, param);
            param.setDestProduceDate(DateUtils.parseDateToStr("yyyy-MM-dd", recordAllotDetailVo.getDestProduceDate()));
            param.setOrignProduceDate(DateUtils.parseDateToStr("yyyy-MM-dd", recordAllotDetailVo.getOrignProduceDate()));
            pdfList.add(param);
        }

        String filename = bound_index + ".pdf";
        exportPDF(response, "jasper/AllotInfo.jasper", parameters, pdfList, filename);
    }


    /**
     * 仓库用料单导出（pdf）
     */
    @PostMapping("/exportRecordUseOutPdf")
    public void exportRecordUseOutPdf(HttpServletResponse response, @RequestBody JSONObject idInJson) throws UnsupportedEncodingException {
        String bound_index = idInJson.getString("boundIndex");
        RecordUseOutbound inout = warehouseUseOutboundService.queryObjByIndex(bound_index);
        Integer state = inout.getState();
        Map<String, Object> parameters = new HashMap<String, Object>();
        parameters.put("BOUND_INDEX", inout.getBoundIndex());
        parameters.put("TOTAL_NUM", inout.getTotalNum());
        parameters.put("STATE", state == 0 ? "未送审" : state == 1 ? "审核中" : "锁单");
        parameters.put("REMARK", inout.getRemark());
        parameters.put("RECORDER", inout.getRecorder());
        parameters.put("LOCK_DATE", Optional.ofNullable(inout.getLockDate()).map(date -> DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", date)).orElse(""));
        parameters.put("RECORD_DATE", Optional.ofNullable(inout.getRecordDate()).map(date -> DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", date)).orElse(""));
//        parameters.put("LOGO",Optional.ofNullable(ProjectSysConfigService.getSysConfigValByType(CommonConstant.ProjectSysConfig.LOGO_PATH)).orElse("/home/<USER>/control_system/speedbot.png"));

        List<MaterialDetailExcelData> pdfList = getMaterialDetailExcelData(bound_index);
        String filename = bound_index + ".pdf";
        exportPDF(response, "jasper/RecordUseOut.jasper", parameters, pdfList, filename);
    }

    /**
     * 处理物料明细
     */
    private List<MaterialDetailExcelData> getMaterialDetailExcelData(String bound_index) {
        List<MaterialDetailExcelData> pdfList = new ArrayList<>();
        QueryParamVO queryParamVO = new QueryParamVO();
        queryParamVO.setKeyWord(bound_index);
        List<RecordInoutDetailVo> list = recordInoutDetailService.queryRecordInoutDetail(queryParamVO);
        for (RecordInoutDetailVo recordInoutDetailVo : list) {
            MaterialDetailExcelData param = new MaterialDetailExcelData();
            BeanUtils.copyProperties(recordInoutDetailVo, param);
            param.setMaterialLocation(recordInoutDetailVo.getPositionName() + "-" + recordInoutDetailVo.getLevelName() + "-" + recordInoutDetailVo.getShelfName());
            param.setProduceDate(DateUtils.parseDateToStr("yyyy-MM-dd", recordInoutDetailVo.getProduceDate()));
            pdfList.add(param);
        }
        return pdfList;
    }


    /**
     * Jasper导出PDF
     */
    private void exportPDF(HttpServletResponse response, String jasperPath, Map<String, Object> parameters, List<MaterialDetailExcelData> pdfList, String fileName) throws UnsupportedEncodingException {
        InputStream resourceAsStream = null;
        File jasperFile = null;
        try {
            ClassPathResource classPathResource = new ClassPathResource(jasperPath);
            resourceAsStream = classPathResource.getInputStream();
        } catch (IOException exception) {
            logger.error(exception.getMessage());
        }
        JasperPrint jasperPrint = null;
        try {
            JasperReport jasperReport;
            if (resourceAsStream == null) {
                jasperReport = (JasperReport) JRLoader.loadObject(jasperFile);
            } else {
                jasperReport = (JasperReport) JRLoader.loadObject(resourceAsStream);
            }
            jasperPrint = JasperFillManager.fillReport(jasperReport, parameters, new TaskReportBBSource(pdfList));
        } catch (JRException e) {
            e.printStackTrace();
        }
        // 4. 设置响应头
        response.setContentType("application/pdf");
        response.setHeader("Content-Disposition",
                "attachment;filename=" + new String(fileName.getBytes("UTF-8"), "ISO8859-1") + ".pdf");
        ReportUtil.reportBasicsPdf(jasperPrint, response, fileName);
    }


}